<?php

namespace Modules\Authentication\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Modules\User\Entities\User;

class UserProfileService
{
    /**
     * Create user profile by user ID (exact copy from accounts LoginController)
     *
     * @param int $userId
     * @return mixed
     */
    public function createUserProfileByUserId(int $userId)
    {
        // For now, just log that this would create a user profile
        // This will be implemented when UserProfile model is available
        Log::info("UserProfileService@createUserProfileByUserId called for user: {$userId}");

        try {
            // This is the exact logic from accounts, but we'll implement when models are ready
            // UserProfile::create([
            //     'user_id' => $userId,
            //     'skills' => (object)[],
            //     'experiences' => [],
            //     'educations' => [],
            //     'projects' => [],
            //     'languages' => [],
            //     'interests' => [],
            //     'references' => [],
            //     'activities' => [],
            //     'certificates' => [],
            //     'additionals' => [],
            //     'completed_sections' => [],
            // ]);

            return true; // Return success for now
        } catch (\Exception $ex) {
            Log::error("UserProfileService@createUserProfileByUserId: " . $ex->getMessage());
            return false;
        }
    }

    /**
     * Create user main CV (exact copy from accounts LoginController)
     *
     * @param int $userId
     * @param int|null $userProfileId
     * @return mixed
     */
    public function createUserMainCv(int $userId, ?int $userProfileId = null)
    {
        // For now, just log that this would create a main CV
        // This will be implemented when CV model is available
        Log::info("UserProfileService@createUserMainCv called for user: {$userId}, profile: {$userProfileId}");

        try {
            // This is the exact logic from accounts, but we'll implement when models are ready
            // The accounts logic creates a main CV for the user

            return true; // Return success for now
        } catch (\Exception $ex) {
            Log::error("UserProfileService@createUserMainCv: " . $ex->getMessage());
            return false;
        }
    }

    /**
     * Create search candidate entry (exact copy from accounts LoginController)
     *
     * @param int $userId
     * @return bool
     */
    public function createSearchCandidateByUserId(int $userId): bool
    {
        // For now, just log that this would create search candidate entry
        // This will be implemented when search_candidates table is available
        Log::info("UserProfileService@createSearchCandidateByUserId called for user: {$userId}");

        try {
            // This is the exact logic from accounts, but we'll implement when table is ready
            // The accounts logic inserts into search_candidates table

            return true; // Return success for now
        } catch (\Exception $ex) {
            Log::error("UserProfileService@createSearchCandidateByUserId: " . $ex->getMessage());
            return false;
        }
    }

    /**
     * Restore user if deleted (match accounts logic)
     *
     * @param User $user
     * @return void
     */
    public function restoreIfUserHasDeleted(User $user): void
    {
        if ($user->trashed()) {
            $user->restore();
            $user->wasRecentlyCreated = true;
        }

        // Create user profile
        $userProfile = $this->createUserProfileByUserId($user->id);

        // Create main CV
        if ($userProfile) {
            $this->createUserMainCv($user->id, $userProfile->id);
        }

        // Create search candidate entry
        $this->createSearchCandidateByUserId($user->id);
    }

    /**
     * Complete user setup after registration (match accounts logic)
     *
     * @param User $user
     * @return void
     */
    public function completeUserSetup(User $user): void
    {
        try {
            DB::transaction(function () use ($user) {
                // Create user profile
                $userProfile = $this->createUserProfileByUserId($user->id);

                // Create main CV
                if ($userProfile) {
                    $this->createUserMainCv($user->id, $userProfile->id);
                }

                // Create search candidate entry
                $this->createSearchCandidateByUserId($user->id);

                Log::info("User setup completed for user: {$user->id}");
            });
        } catch (\Exception $ex) {
            Log::error("UserProfileService@completeUserSetup: " . $ex->getMessage());
        }
    }

    /**
     * Add tracking UTM parameters (match accounts logic)
     *
     * @param \Illuminate\Http\Request $request
     * @param User $user
     * @return void
     */
    public function addTrackingUtm($request, User $user): void
    {
        try {
            $utmData = [];

            // Collect UTM parameters
            $utmParams = ['utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content'];
            foreach ($utmParams as $param) {
                if ($request->has($param)) {
                    $utmData[$param] = $request->get($param);
                }
            }

            // Store UTM data if available
            if (!empty($utmData)) {
                // Check if UserTracking model exists
                $trackingClass = 'Modules\\User\\Entities\\UserTracking';
                if (class_exists($trackingClass)) {
                    $trackingClass::updateOrCreate(
                        ['user_id' => $user->id],
                        array_merge($utmData, [
                            'ip_address' => $request->ip(),
                            'user_agent' => $request->userAgent(),
                            'referrer' => $request->header('referer'),
                        ])
                    );
                }

                Log::info("UTM tracking added for user: {$user->id}", $utmData);
            }
        } catch (\Exception $ex) {
            Log::error("UserProfileService@addTrackingUtm: " . $ex->getMessage());
        }
    }
}
