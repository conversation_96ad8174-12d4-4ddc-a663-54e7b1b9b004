<?php

namespace Modules\Authentication\Entities;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;
use Modules\User\Entities\User;

/**
 * Class Social
 *
 * @package Modules\Authentication\Entities
 *
 * @property int $id
 * @property int $user_id
 * @property string $provider_user_id
 * @property string $provider
 * @property string|null $email
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read User $user
 * @method static Builder|Social newModelQuery()
 * @method static Builder|Social newQuery()
 * @method static Builder|Social query()
 * @method static Builder|Social whereCreatedAt($value)
 * @method static Builder|Social whereEmail($value)
 * @method static Builder|Social whereId($value)
 * @method static Builder|Social whereProvider($value)
 * @method static Builder|Social whereProviderUserId($value)
 * @method static Builder|Social whereUpdatedAt($value)
 */
class Social extends Model
{
    /**
     * @inheritdoc
     */
    protected $connection = 'mysql_accounts';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'socials';

    protected $fillable = [
        'user_id',
        'provider_user_id',
        'provider',
        'name',
        'email',
        'avatar',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
