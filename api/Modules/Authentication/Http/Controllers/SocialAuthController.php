<?php

namespace Modules\Authentication\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Laravel\Socialite\Facades\Socialite;
use Modules\Authentication\Entities\Social;
use Modules\Authentication\Events\SignUp;
use Modules\Authentication\Services\TokenCookieService;
use Modules\Authentication\Services\UserProfileService;
use Modules\User\Entities\User;
use Modules\User\Events\NewUserProcessed;

class SocialAuthController extends Controller
{
    /**
     * Supported social providers
     */
    protected $supportedProviders = ['facebook', 'twitter', 'google', 'linkedin', 'github', 'apple'];

    /**
     * Token cookie service
     */
    protected $tokenCookieService;

    /**
     * User profile service
     */
    protected $userProfileService;

    public function __construct(
        TokenCookieService $tokenCookieService,
        UserProfileService $userProfileService
    ) {
        $this->tokenCookieService = $tokenCookieService;
        $this->userProfileService = $userProfileService;
    }

    /**
     * Get social login redirect URL
     */
    public function redirectToProvider($provider)
    {
        if (!in_array($provider, $this->supportedProviders)) {
            return response()->json([
                'success' => false,
                'message' => 'Unsupported provider'
            ], 400);
        }

        try {
            $redirectUrl = Socialite::driver($provider)->redirect()->getTargetUrl();

            return response()->json([
                'success' => true,
                'message' => 'Redirect URL generated successfully',
                'data' => [
                    'redirect_url' => $redirectUrl
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate redirect URL: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle social provider callback
     */
    public function handleProviderCallback(Request $request, $provider)
    {
        if (!in_array($provider, $this->supportedProviders)) {
            return response()->json([
                'success' => false,
                'message' => 'Unsupported provider'
            ], 400);
        }

        try {
            // Handle different callback methods based on provider
            $socialUser = $this->getSocialUser($request, $provider);

            if (!$socialUser) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to get user information from ' . $provider
                ], 400);
            }

            // Validate required fields
            if (!$socialUser->getEmail()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Email is required from social provider'
                ], 400);
            }

            // Find or create user (sử dụng logic có sẵn từ LoginController)
            $user = $this->findOrCreateUser($socialUser, $provider, $request);

            if ($user->isFreezing()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Account is frozen'
                ], 403);
            }

            // Create token
            $token = $user->createToken('API Token')->accessToken;

            // Fire login event
            event(new \Illuminate\Auth\Events\Login('api', $user, false));

            // Set cookies for multi-subdomain access
            $this->tokenCookieService->setAllAuthCookies($token, null, $user->id);

            // Send employer login notification if user is employer
            if ($user->isEmployer()) {
                $user->sendEmployerLoginNotification();
            }

            return response()->json([
                'success' => true,
                'message' => 'Social login successful',
                'data' => [
                    'user' => [
                        'id' => $user->id,
                        'email' => $user->email,
                        'display_name' => $user->display_name,
                        'type' => $user->type,
                        'is_employer' => $user->isEmployer(),
                        'has_verified_email' => $user->hasVerifiedEmail(),
                        'has_approved_account' => $user->hasApprovedAccount(),
                    ],
                    'access_token' => $token,
                    'token_type' => 'Bearer',
                    'provider' => $provider,
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Social login failed', [
                'provider' => $provider,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Social login failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get linked social accounts
     */
    public function getLinkedAccounts(Request $request)
    {
        $user = $request->user();
        $socials = Social::where('user_id', $user->id)->get();

        return response()->json([
            'success' => true,
            'data' => [
                'linked_accounts' => $socials->map(function ($social) {
                    return [
                        'provider' => $social->provider,
                        'provider_user_id' => $social->provider_user_id,
                        'name' => $social->name,
                        'email' => $social->email,
                        'linked_at' => $social->created_at,
                    ];
                })
            ]
        ]);
    }

    /**
     * Link social account to current user
     */
    public function linkAccount(Request $request, $provider)
    {
        if (!in_array($provider, $this->supportedProviders)) {
            return response()->json([
                'success' => false,
                'message' => 'Unsupported provider'
            ], 400);
        }

        try {
            $socialUser = Socialite::driver($provider)->user();
            $user = $request->user();

            // Check if social account is already linked to another user
            $existingSocial = Social::where('provider', $provider)
                ->where('provider_user_id', $socialUser->getId())
                ->first();

            if ($existingSocial && $existingSocial->user_id !== $user->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'This social account is already linked to another user'
                ], 409);
            }

            // Create or update social link
            Social::updateOrCreate(
                [
                    'user_id' => $user->id,
                    'provider' => $provider,
                ],
                [
                    'provider_user_id' => $socialUser->getId(),
                    'name' => $socialUser->getName(),
                    'email' => $socialUser->getEmail(),
                    'avatar' => $socialUser->getAvatar(),
                ]
            );

            return response()->json([
                'success' => true,
                'message' => 'Social account linked successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to link social account: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Unlink social account
     */
    public function unlinkAccount(Request $request, $provider)
    {
        $user = $request->user();

        /** @var Social $social */
        $social = Social::query()->where('user_id', $user->id)
            ->where('provider', $provider)
            ->first();

        if (!$social) {
            return response()->json([
                'success' => false,
                'message' => 'Social account not found'
            ], 404);
        }

        $social->delete();

        return response()->json([
            'success' => true,
            'message' => 'Social account unlinked successfully'
        ]);
    }


    /**
     * Get social user based on provider and request data
     */
    protected function getSocialUser(Request $request, $provider)
    {
        try {
            switch ($provider) {
                case 'facebook':
                case 'google':
                case 'linkedin':
                case 'github':
                case 'apple':
                    // For these providers, use standard OAuth flow
                    if ($request->has('code')) {
                        return Socialite::driver($provider)->user();
                    }
                    // For token-based authentication (mobile apps)
                    if ($request->has('access_token')) {
                        return Socialite::driver($provider)->userFromToken($request->access_token);
                    }
                    break;

                case 'twitter':
                    // Twitter uses OAuth 1.0a with token and token_secret
                    if ($request->has('oauth_token') && $request->has('oauth_verifier')) {
                        return Socialite::driver($provider)->user();
                    }
                    // For token-based authentication
                    if ($request->has('access_token') && $request->has('access_token_secret')) {
                        return Socialite::driver($provider)->userFromTokenAndSecret(
                            $request->access_token,
                            $request->access_token_secret
                        );
                    }
                    break;
            }

            // Fallback to standard user() method
            return Socialite::driver($provider)->user();

        } catch (\Exception $e) {
            Log::error('Failed to get social user', [
                'provider' => $provider,
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);
            return null;
        }
    }

    /**
     * Find or create user from social provider (sử dụng logic từ LoginController)
     *
     * @throws \Throwable
     */
    protected function findOrCreateUser($socialUser, $provider, Request $request)
    {
        return DB::transaction(function () use ($socialUser, $provider, $request) {
            // First, check if social account exists
            /** @var Social $social */
            $social = Social::query()->where('provider', $provider)
                ->where('provider_user_id', $socialUser->getId())
                ->first();

            if ($social) {
                return $social->user;
            }

            // Check if user exists by email
            $user = User::where('email', $socialUser->getEmail())->first();

            $isNewUser = false;

            if (!$user) {
                $isNewUser = true;
                // Create new user (match accounts logic)
                $user = User::create([
                    'email' => $socialUser->getEmail(),
                    'display_name' => $socialUser->getName() ?: $socialUser->getNickname() ?: $socialUser->getEmail(),
                    'type' => User::RESUME_TYPE,
                    'email_verified_at' => now(),
                ]);

                // Complete user setup (match accounts logic)
                $this->userProfileService->completeUserSetup($user);

                // Add tracking UTM if available
                $this->userProfileService->addTrackingUtm($request, $user);

                // Fire events for new user (match accounts exactly)
                event(new \Illuminate\Auth\Events\Registered($user));
                event(new NewUserProcessed($user));
                event(new SignUp($user, [
                    'method' => $provider,
                    'provider' => $provider,
                    'user_agent' => $request->userAgent(),
                    'ip_address' => $request->ip(),
                ]));

                Log::info('New user created via social login', [
                    'user_id' => $user->id,
                    'email' => $user->email,
                    'provider' => $provider,
                    'provider_user_id' => $socialUser->getId(),
                ]);
            } else {
                // Restore user if deleted (match accounts logic)
                $this->userProfileService->restoreIfUserHasDeleted($user);
            }

            // Create social link
            Social::create([
                'user_id' => $user->id,
                'provider' => $provider,
                'provider_user_id' => $socialUser->getId(),
                'name' => $socialUser->getName(),
                'email' => $socialUser->getEmail(),
                'avatar' => $socialUser->getAvatar(),
            ]);

            Log::info('Social account linked', [
                'user_id' => $user->id,
                'provider' => $provider,
                'provider_user_id' => $socialUser->getId(),
                'is_new_user' => $isNewUser,
            ]);

            return $user;
        });
    }
}
