# API Endpoints và Curl Tests - Authentication Module

## 🚀 **Tất cả API Endpoints:**

### **1. Public Endpoints (Không cần authentication)**

#### **Test Endpoint:**
- `GET /api/test` - Test module hoạt động

#### **Authentication:**
- `POST /api/auth/login` - <PERSON><PERSON><PERSON> nhập email/password
- `POST /api/auth/register` - Đăng ký tài khoản

#### **Social Authentication:**
- `GET /api/auth/social/{provider}` - Lấy redirect URL cho social login
- `POST /api/auth/social/{provider}/callback` - Xử lý callback từ social provider

#### **Verification:**
- `POST /api/auth/verification/send` - G<PERSON>i mã xác thực (placeholder)
- `POST /api/auth/verification/verify` - <PERSON><PERSON><PERSON> thực mã (placeholder)

### **2. Protected Endpoints (Cần auth.api middleware)**

#### **User Profile:**
- `GET /api/auth/me` - Lấy thông tin user hiện tại
- `POST /api/auth/logout` - Đăng xuất

#### **Social Account Management:**
- `GET /api/auth/social/accounts` - Lấy danh sách tài khoản social đã liên kết
- `POST /api/auth/social/{provider}/link` - Liên kết tài khoản social
- `DELETE /api/auth/social/{provider}/unlink` - Hủy liên kết tài khoản social

### **3. Employer-only Endpoints (Cần auth.employer middleware)**

#### **Employer Dashboard:**
- `GET /api/employer/dashboard` - Dashboard cho employer
- `GET /api/employer/profile` - Profile employer

---

## 🧪 **Curl Tests:**

### **1. Test Module:**
```bash
curl -X GET http://localhost:8000/api/test \
  -H "Content-Type: application/json"

# Expected Response:
{
    "success": true,
    "message": "Authentication module is working!",
    "timestamp": "2024-01-01T00:00:00.000000Z",
    "module": "Authentication",
    "version": "1.0.0"
}
```

### **2. User Registration:**
```bash
curl -X POST http://localhost:8000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "display_name": "Test User",
    "type": "resume"
  }'

# Expected Response:
{
    "success": true,
    "message": "Registration successful",
    "data": {
        "user": {
            "id": "uuid",
            "email": "<EMAIL>",
            "display_name": "Test User",
            "type": "resume",
            "is_employer": false,
            "has_verified_email": true,
            "has_approved_account": true
        },
        "access_token": "token_here",
        "token_type": "Bearer"
    }
}
```

### **3. User Login:**
```bash
curl -X POST http://localhost:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'

# Expected Response:
{
    "success": true,
    "message": "Login successful",
    "data": {
        "user": {
            "id": "uuid",
            "email": "<EMAIL>",
            "display_name": "Test User",
            "type": "resume",
            "is_employer": false,
            "has_verified_email": true,
            "has_approved_account": true
        },
        "access_token": "token_here",
        "token_type": "Bearer"
    }
}
```

### **4. Employer Registration:**
```bash
curl -X POST http://localhost:8000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "display_name": "Company HR",
    "type": "employer"
  }'

# Expected Response:
{
    "success": true,
    "message": "Registration successful",
    "data": {
        "user": {
            "id": "uuid",
            "email": "<EMAIL>",
            "display_name": "Company HR",
            "type": "employer",
            "is_employer": true,
            "has_verified_email": false,
            "has_approved_account": false
        },
        "access_token": "token_here",
        "token_type": "Bearer"
    }
}
```

### **5. Get Current User (Protected):**
```bash
# Lấy token từ login response trước
TOKEN="your_access_token_here"

curl -X GET http://localhost:8000/api/auth/me \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN"

# Expected Response:
{
    "success": true,
    "message": "User retrieved successfully",
    "data": {
        "user": {
            "id": "uuid",
            "email": "<EMAIL>",
            "display_name": "Test User",
            "type": "resume",
            "is_employer": false
        }
    }
}
```

### **6. Logout (Protected):**
```bash
curl -X POST http://localhost:8000/api/auth/logout \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN"

# Expected Response:
{
    "success": true,
    "message": "Logout successful"
}
```

### **7. Social Login - Get Redirect URL:**
```bash
curl -X GET http://localhost:8000/api/auth/social/google \
  -H "Content-Type: application/json"

# Expected Response:
{
    "success": true,
    "message": "Redirect URL generated successfully",
    "data": {
        "redirect_url": "https://accounts.google.com/oauth/authorize?..."
    }
}
```

### **8. Social Login - Handle Callback:**
```bash
curl -X POST http://localhost:8000/api/auth/social/google/callback \
  -H "Content-Type: application/json" \
  -d '{
    "code": "authorization_code_from_google",
    "state": "csrf_token"
  }'

# Expected Response:
{
    "success": true,
    "message": "Social login successful",
    "data": {
        "user": {
            "id": "uuid",
            "email": "<EMAIL>",
            "display_name": "Google User",
            "type": "resume",
            "is_employer": false
        },
        "access_token": "token_here",
        "token_type": "Bearer",
        "provider": "google"
    }
}
```

### **9. Get Linked Social Accounts (Protected):**
```bash
curl -X GET http://localhost:8000/api/auth/social/accounts \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN"

# Expected Response:
{
    "success": true,
    "data": {
        "linked_accounts": [
            {
                "provider": "google",
                "provider_user_id": "*********",
                "name": "Google User",
                "email": "<EMAIL>",
                "linked_at": "2024-01-01T00:00:00.000000Z"
            }
        ]
    }
}
```

### **10. Employer Dashboard (Employer-only):**
```bash
# Cần login với employer account trước
EMPLOYER_TOKEN="employer_access_token_here"

curl -X GET http://localhost:8000/api/employer/dashboard \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $EMPLOYER_TOKEN"

# Expected Response:
{
    "success": true,
    "message": "Welcome to employer dashboard!",
    "user": {
        "id": "uuid",
        "email": "<EMAIL>",
        "type": "employer",
        "is_employer": true
    }
}
```

### **11. Test Error Cases:**

#### **Invalid Login:**
```bash
curl -X POST http://localhost:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "wrongpassword"
  }'

# Expected Response:
{
    "success": false,
    "message": "Invalid credentials"
}
```

#### **Unauthenticated Access:**
```bash
curl -X GET http://localhost:8000/api/auth/me \
  -H "Content-Type: application/json"

# Expected Response:
{
    "success": false,
    "message": "Unauthenticated"
}
```

#### **Non-employer Access to Employer Route:**
```bash
# Sử dụng regular user token
curl -X GET http://localhost:8000/api/employer/dashboard \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $REGULAR_USER_TOKEN"

# Expected Response:
{
    "success": false,
    "message": "Access denied. Employer account required."
}
```

---

## 🔧 **Middleware Usage:**

### **auth.api middleware:**
- Xác thực user từ token (cookie hoặc Authorization header)
- Check account không bị frozen
- Sử dụng UserResolver để resolve user từ multiple sources

### **auth.employer middleware:**
- Extends auth.api middleware
- Check user là employer
- Check employer account đã được approve
- Check account không bị frozen

---

## 📝 **Notes:**

1. **Cookies**: Tất cả login/register sẽ set cookies cho multi-subdomain (.topdev.vn)
2. **Events**: Login/register sẽ fire các events tương ứng
3. **Logging**: Tất cả actions đều được log chi tiết
4. **Employer Notifications**: Employer login sẽ gửi notification
5. **UTM Tracking**: Registration sẽ track UTM parameters nếu có
6. **User Profile**: Registration sẽ tự động tạo user profile, CV, search candidate entry
