# Social Authentication API Documentation

## Tổng quan

API này cung cấp các endpoint để xử lý đăng nhập qua các social providers như Facebook, Twitter, LinkedIn, Google, GitHub, và Apple.

## Supported Providers

- `facebook` - Facebook Login
- `twitter` - Twitter/X.com Login  
- `linkedin` - LinkedIn Login
- `google` - Google Login
- `github` - GitHub Login
- `apple` - Apple Sign In

## Authentication Flow

### 1. Web-based OAuth Flow

#### Step 1: Get Authorization URL
```http
GET /api/auth/social/{provider}
```

**Response:**
```json
{
    "success": true,
    "message": "Redirect URL generated successfully",
    "data": {
        "redirect_url": "https://provider.com/oauth/authorize?client_id=..."
    }
}
```

#### Step 2: Handle Callback
```http
POST /api/auth/social/{provider}/callback
Content-Type: application/json

{
    "code": "authorization_code_from_provider"
}
```

### 2. Token-based Authentication (Mobile Apps)

#### Facebook, Google, LinkedIn, GitHub, Apple
```http
POST /api/auth/social/{provider}/callback
Content-Type: application/json

{
    "access_token": "token_from_provider"
}
```

#### Twitter (OAuth 1.0a)
```http
POST /api/auth/social/{provider}/callback
Content-Type: application/json

{
    "access_token": "oauth_token",
    "access_token_secret": "oauth_token_secret"
}
```

## API Endpoints

### 1. Get Social Login URL

**Endpoint:** `GET /api/auth/social/{provider}`

**Parameters:**
- `provider` (path): One of: facebook, twitter, linkedin, google, github, apple

**Response:**
```json
{
    "success": true,
    "message": "Redirect URL generated successfully", 
    "data": {
        "redirect_url": "https://provider.com/oauth/authorize?..."
    }
}
```

**Error Response:**
```json
{
    "success": false,
    "message": "Unsupported provider"
}
```

### 2. Handle Social Login Callback

**Endpoint:** `POST /api/auth/social/{provider}/callback`

**Parameters:**
- `provider` (path): One of: facebook, twitter, linkedin, google, github, apple

**Request Body (OAuth Code Flow):**
```json
{
    "code": "authorization_code_from_provider"
}
```

**Request Body (Token Flow - Facebook, Google, LinkedIn, GitHub, Apple):**
```json
{
    "access_token": "access_token_from_provider"
}
```

**Request Body (Token Flow - Twitter):**
```json
{
    "access_token": "oauth_token",
    "access_token_secret": "oauth_token_secret"
}
```

**Success Response:**
```json
{
    "success": true,
    "message": "Social login successful",
    "data": {
        "user": {
            "id": "uuid",
            "email": "<EMAIL>",
            "display_name": "John Doe",
            "type": "resume",
            "is_employer": false,
            "has_verified_email": true,
            "has_approved_account": true
        },
        "access_token": "api_access_token",
        "token_type": "Bearer",
        "provider": "facebook"
    }
}
```

**Error Responses:**
```json
{
    "success": false,
    "message": "Unsupported provider"
}
```

```json
{
    "success": false,
    "message": "Email is required from social provider"
}
```

```json
{
    "success": false,
    "message": "Account is frozen"
}
```

### 3. Get Linked Social Accounts

**Endpoint:** `GET /api/auth/social/accounts`

**Headers:**
```
Authorization: Bearer {access_token}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "linked_accounts": [
            {
                "provider": "facebook",
                "provider_user_id": "*********",
                "name": "John Doe",
                "email": "<EMAIL>",
                "linked_at": "2025-08-05T10:30:00Z"
            }
        ]
    }
}
```

### 4. Link Social Account

**Endpoint:** `POST /api/auth/social/{provider}/link`

**Headers:**
```
Authorization: Bearer {access_token}
```

**Request Body:**
```json
{
    "access_token": "social_provider_token"
}
```

**Response:**
```json
{
    "success": true,
    "message": "Social account linked successfully"
}
```

### 5. Unlink Social Account

**Endpoint:** `DELETE /api/auth/social/{provider}/unlink`

**Headers:**
```
Authorization: Bearer {access_token}
```

**Response:**
```json
{
    "success": true,
    "message": "Social account unlinked successfully"
}
```

## Provider-specific Notes

### Facebook
- Requires `FACEBOOK_APP_ID` and `FACEBOOK_APP_SECRET`
- Supports both OAuth code flow and token-based authentication
- Returns user's name, email, and profile picture

### Twitter/X.com
- Requires `TWITTER_APP_ID` and `TWITTER_APP_SECRET`
- Uses OAuth 1.0a (requires token and token_secret)
- May not always return email (depends on app permissions)

### LinkedIn
- Requires `LINKEDIN_APP_ID` and `LINKEDIN_APP_SECRET`
- Supports both OAuth code flow and token-based authentication
- Returns professional profile information

### Google
- Requires `GOOGLE_APP_ID` and `GOOGLE_APP_SECRET`
- Supports both OAuth code flow and token-based authentication
- Reliable email and profile information

### GitHub
- Requires `GITHUB_CLIENT_ID` and `GITHUB_CLIENT_SECRET`
- Supports both OAuth code flow and token-based authentication
- May not return email if user's email is private

### Apple
- Requires `SIGN_IN_WITH_APPLE_CLIENT_ID` and `SIGN_IN_WITH_APPLE_CLIENT_SECRET`
- Uses JWT-based authentication
- Privacy-focused (may provide relay email)

## Error Handling

All endpoints return consistent error responses:

```json
{
    "success": false,
    "message": "Error description"
}
```

Common HTTP status codes:
- `400` - Bad Request (invalid provider, missing parameters)
- `401` - Unauthorized (invalid token)
- `403` - Forbidden (account frozen)
- `404` - Not Found (social account not found)
- `409` - Conflict (social account already linked to another user)
- `500` - Internal Server Error

## Database Schema

### Socials Table
```sql
CREATE TABLE socials (
    id BIGINT PRIMARY KEY,
    user_id UUID NOT NULL,
    provider VARCHAR(255) NOT NULL,
    provider_user_id VARCHAR(255) NOT NULL,
    name VARCHAR(255) NULL,
    email VARCHAR(255) NULL,
    avatar TEXT NULL,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,

    INDEX idx_user_provider (user_id, provider),
    UNIQUE KEY unique_provider_user (provider, provider_user_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

## Configuration

Add these environment variables to your `.env` file:

```env
# Facebook
FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret
FACEBOOK_APP_CALLBACK_URL=http://your-domain.com/api/auth/social/facebook/callback

# Twitter
TWITTER_APP_ID=your_twitter_app_id
TWITTER_APP_SECRET=your_twitter_app_secret
TWITTER_APP_CALLBACK_URL=http://your-domain.com/api/auth/social/twitter/callback

# LinkedIn
LINKEDIN_APP_ID=your_linkedin_app_id
LINKEDIN_APP_SECRET=your_linkedin_app_secret
LINKEDIN_APP_CALLBACK_URL=http://your-domain.com/api/auth/social/linkedin/callback

# Google
GOOGLE_APP_ID=your_google_app_id
GOOGLE_APP_SECRET=your_google_app_secret
GOOGLE_APP_CALLBACK_URL=http://your-domain.com/api/auth/social/google/callback

# GitHub
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret
GITHUB_APP_CALLBACK_URL=http://your-domain.com/api/auth/social/github/callback

# Apple
SIGN_IN_WITH_APPLE_CLIENT_ID=your_apple_client_id
SIGN_IN_WITH_APPLE_CLIENT_SECRET=your_apple_client_secret
SIGN_IN_WITH_APPLE_REDIRECT=http://your-domain.com/api/auth/social/apple/callback
```
