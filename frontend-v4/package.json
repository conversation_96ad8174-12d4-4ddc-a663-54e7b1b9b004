{"name": "frontend-v4", "version": "0.1.0", "private": true, "scripts": {"dev": "NODE_OPTIONS=--max-old-space-size=4096 next dev", "build": "next build", "start": "node .next/standalone/server.js", "lint": "next lint", "format": "prettier --check --ignore-path .gitignore .", "format:fix": "prettier --write --ignore-path .gitignore .", "cypress:open": "cypress open", "cypress:component": "cypress run --component --quiet --headless", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@headlessui/react": "1.7.16", "@jsmonday/imgproxy": "^0.1.0", "@lottiefiles/react-lottie-player": "^3.5.3", "@next/third-parties": "^14.0.1", "@react-spring/web": "^9.7.3", "@reduxjs/toolkit": "1.9.5", "@sindresorhus/slugify": "^2.2.1", "@tailwindcss/typography": "^0.5.10", "@tinymce/tinymce-react": "^4.3.0", "@topdev/topdev-header": "git+https://git.topdev.asia/topdev-package/topdev-header.git#v1.1.20", "@types/node": "20.19.0", "@types/react": "18.2.21", "@types/react-dom": "18.2.7", "@uidotdev/usehooks": "2.1.0", "array-move": "^4.0.0", "autoprefixer": "10.4.14", "axios": "1.4.0", "cache-manager": "^5.2.4", "cache-manager-redis-yet": "^4.1.2", "dayjs": "1.11.6", "eslint": "8.57.0", "eslint-config-next": "14.0.1", "flowbite": "1.8.1", "flowbite-react": "0.6.4", "formik": "2.4.5", "js-cookie": "3.0.5", "next": "14.1.0", "next-auth": "^4.24.11", "next-intl": "3.5.2", "nextjs-toploader": "^1.4.2", "pm2": "5.3.0", "postcss": "8.4.29", "react": "^18.2.0", "react-device-detect": "2.2.3", "react-dom": "^18.2.0", "react-google-recaptcha": "^3.1.0", "react-hook-form": "7.45.4", "react-icons": "4.10.1", "react-redux": "8.1.2", "react-router-dom": "^6.21.1", "react-select": "^5.7.5", "react-sortablejs": "^6.1.4", "react-tailwindcss-datepicker": "^1.6.6", "sass": "1.66.1", "sharp": "^0.32.6", "simplebar-react": "3.2.4", "slugify": "^1.6.6", "sortablejs": "^1.15.0", "sweetalert2": "11.7.27", "swiper": "10.3.1", "unleash-proxy-client": "^3.4.0", "usehooks-ts": "2.9.1", "yup": "1.2.0"}, "devDependencies": {"@chromatic-com/storybook": "^4.0.1", "@storybook/addon-a11y": "^9.0.16", "@storybook/addon-docs": "^9.0.16", "@storybook/addon-onboarding": "^9.0.16", "@storybook/nextjs": "^9.0.16", "@tailwindcss/forms": "0.5.4", "@types/axios": "0.14.0", "@types/js-cookie": "^3.0.4", "@types/react-google-recaptcha": "^2.1.9", "@types/react-redux": "7.1.25", "@types/sortablejs": "^1.15.5", "cypress": "13.6.3", "eslint-plugin-storybook": "^9.0.16", "playwright": "^1.53.2", "prettier": "^3.0.1", "prettier-plugin-tailwindcss": "^0.5.1", "storybook": "^9.0.16", "tailwind-scrollbar": "^3.0.5", "tailwindcss": "3.3.3", "typescript": "5.2.2"}, "packageManager": "yarn@4.9.2"}