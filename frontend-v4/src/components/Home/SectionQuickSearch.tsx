"use client";

import CardJobQuickLinkList from "@/components/Card/Job/CardJobQuickLinkList";
import {
  QUICK_LINKS_BY_COMPANIES,
  QUICK_LINKS_BY_FRESHER,
  QUICK_LINKS_BY_INDUSTRIES,
} from "@/contansts/links";
import { useTranslations } from "next-intl";
import { useState } from "react";

type Props = {
  srcPage?: string;
  mediumPage?: string;
};

export default function SectionQuickSearch({
  srcPage = "home",
  mediumPage = "quicksearch",
}: Props) {
  const t = useTranslations();
  const [activeItem, setActiveItem] = useState<number | null>(-1);

  const handleCollapseClick = (cardId: number | null) => {
    if (cardId === activeItem) {
      cardId = null;
    }

    setActiveItem(cardId);
  };

  return (
    <section
      id={"quick-search-container"}
      className={"bg-gray-100 py-8 lg:bg-transparent lg:pb-20 lg:pt-7"}
    >
      <div className={"container flex flex-col gap-4 lg:gap-8"}>
        <h2 className={"text-xl font-bold lg:text-4xl"}>
          {t("home_quick_search")}
        </h2>

        <div className={"md:grid md:grid-cols-3 md:gap-6"}>
          <CardJobQuickLinkList
            title={t("common_top_job_for_fresher")}
            values={QUICK_LINKS_BY_FRESHER}
            collapse={activeItem !== 0}
            onCollapse={() => handleCollapseClick(0)}
            srcPage={srcPage}
            mediumPage={mediumPage}
          />
          <CardJobQuickLinkList
            title={t("common_top_job_for_industry")}
            values={QUICK_LINKS_BY_INDUSTRIES}
            collapse={activeItem !== 1}
            onCollapse={() => handleCollapseClick(1)}
            srcPage={srcPage}
            mediumPage={mediumPage}
          />
          <CardJobQuickLinkList
            title={t("common_top_job_for_company")}
            values={QUICK_LINKS_BY_COMPANIES}
            collapse={activeItem !== 2}
            onCollapse={() => handleCollapseClick(2)}
            srcPage={srcPage}
            mediumPage={mediumPage}
          />
        </div>
      </div>
    </section>
  );
}
