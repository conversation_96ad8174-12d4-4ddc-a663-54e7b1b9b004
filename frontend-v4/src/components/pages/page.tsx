import {
  SectionAds,
  SectionBlogIT,
  SectionFeaturedCompanies,
  SectionHero,
  SectionHighlightCompanies,
  SectionNewJobs,
  SectionPopularCompanies,
  SectionQuickSearch,
  SectionSuperHotJobsToday,
  SectionSuperSpotlightCompanies,
} from "@/components/Home";

import "@/assets/styles/pages/home.scss";
import isDevice from "@/utils/device";
import dynamic from "next/dynamic";
const SmartBannerEvent = dynamic(
  () => import("@/components/Events/SmartBanner"),
  { ssr: false },
);

export default async function HomePage() {
  const device = isDevice();
  return (
    <main>
      {device === "mobile" && <SmartBannerEvent />}
      <SectionHero />
      <SectionSuperSpotlightCompanies />
      <SectionFeaturedCompanies />
      <SectionSuperHotJobsToday />
      <SectionPopularCompanies />
      {device === "desktop" ? <SectionAds /> : ""}
      <SectionHighlightCompanies />
      <SectionNewJobs />
      <SectionBlogIT />
      <SectionQuickSearch />
    </main>
  );
}
