"use client";
import { <PERSON> } from "@/types/page";
import { Header as HeaderPackage } from "@topdev/topdev-header";
import { useLocale } from "next-intl";
import { usePathname } from "next/navigation";
import { useEffect, useState } from "react";
import MenuProduct from "./Header/MenuProduct";
import { useAppDispatch } from "@/store";
import { open } from "@/store/slices/authSlide";

export default function Header() {
  const pathname = usePathname();
  const [isCSSLoaded, setIsCSSLoaded] = useState(false);
  const locale = useLocale();
  const dispatch = useAppDispatch();

  useEffect(() => {
    // Function to check if specific CSS rules are loaded
    const checkCSSLoaded = () => {
      // Create a test element
      const testElement = document.createElement("div");
      testElement.className = "topdev-header"; // Replace with a class from your package
      document.body.appendChild(testElement);

      // Check if styles are applied
      const styles = window.getComputedStyle(testElement);
      const isLoaded = styles.display !== ""; // Adjust this check based on your CSS

      // Cleanup
      document.body.removeChild(testElement);
      return isLoaded;
    };

    // Check periodically until CSS is loaded
    const checkInterval = setInterval(() => {
      if (checkCSSLoaded()) {
        setIsCSSLoaded(true);
        clearInterval(checkInterval);
      }
    }, 100);

    // Cleanup interval
    return () => clearInterval(checkInterval);
  }, []);

  const routerPayment = ["/products", "/carts"];
  const checkShowPayment =
    pathname != "/" &&
    (routerPayment.includes(pathname) || pathname.includes("/result"));
  const noLayoutRoutes = [
    "/nha-tuyen-dung/to-chuc/korean-it-companies-83771",
    "/companies/group/korean-it-companies-83771",
  ];
  const isNoLayout = noLayoutRoutes.includes(pathname);

  if (isNoLayout) return <></>;
  if (checkShowPayment) return <MenuProduct />;

  // Only render HeaderPackage when CSS is loaded
  return isCSSLoaded ? (
    <HeaderPackage
      language={locale as Lang}
      onClickLogout={() => console.log("second")}
      onClickLogin={() => dispatch(open())}
    />
  ) : null;
}
