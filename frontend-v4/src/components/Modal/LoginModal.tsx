"use client";
import { Modal } from "flowbite-react";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { FC, useEffect, useRef } from "react";
import { FcGoogle } from "react-icons/fc";
import { RiFacebookCircleFill } from "react-icons/ri";
import { FaLinkedinIn } from "react-icons/fa";
import Twitter from "../Icons/Twitter";
import { FaApple } from "react-icons/fa";
import { FaGithub } from "react-icons/fa";
import { signIn, signOut } from "next-auth/react";
import { useAppDispatch, useAppSelector } from "@/store";
import { closePopup } from "@/store/slices/authSlide";

interface Props {}

const LoginModal: FC<Props> = () => {
  const t = useTranslations();
  const open = useAppSelector((state) => state?.auth?.open);
  const dispatch = useAppDispatch();
  const modalRef = useRef(null);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        modalRef.current &&
        !(modalRef.current as HTMLElement).contains(event.target as Node)
      ) {
        dispatch(closePopup());
      }
    }

    if (open) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [open, dispatch]);
  return (
    <Modal show={open} size={"xl"}>
      <div ref={modalRef}>
        <Modal.Body className="relative w-[746px] -translate-x-24 overflow-hidden rounded-xl bg-white py-11">
          <div className="flex flex-col items-center">
            <Image
              src="https://c.topdevvn.com/uploads/2025/07/23/login.png"
              width={171}
              height={140}
              alt="login"
            />
            <span className="mt-5 block text-[32px] font-bold text-[#DD3F24]">
              Your career journey continues here
            </span>
            <span className="mt-4 block text-lg text-[#5D5D5D]">
              Log in to update your profile and discover matching opportunities.
            </span>
            <button
              onClick={() =>
                signIn("google", { callbackUrl: "/auth/callback?redirect=/" })
              }
              className="mt-5"
            >
              <span className="flex h-14 w-[454px] items-center justify-center gap-[10px] rounded-full border border-[#3D3D3D] font-medium">
                <FcGoogle className="h-6 w-6" />
                Continue with Google
              </span>
            </button>
            {/* <button className="mt-3">
              <span className="flex h-14 w-[454px] items-center justify-center rounded-full bg-[#3D3D3D] font-medium text-white">
                Continue with Github
              </span>
            </button> */}
            <button className="mt-3">
              <span className="flex h-14 w-[454px] items-center justify-center gap-[10px] rounded-full bg-[#176CF0] font-medium text-white">
                <RiFacebookCircleFill className="h-6 w-6" />
                Continue with Facebook
              </span>
            </button>
            <div className={`mt-4 flex w-[450px] items-center`}>
              <div className="flex-grow border-t border-gray-300" />
              <span className="px-3 text-sm text-gray-500">or log in with</span>
              <div className="flex-grow border-t border-gray-300" />
            </div>
            <div className="mt-4 flex items-center gap-3">
              <button>
                <span className="flex h-[56px] w-[56px]  items-center justify-center rounded-full bg-[#176CF0] font-medium text-white">
                  <FaLinkedinIn className="h-6 w-6" />
                </span>
              </button>
              <button>
                <span className="flex h-[56px] w-[56px] items-center justify-center rounded-full bg-black font-medium text-white">
                  <Twitter />
                </span>
              </button>
              <button>
                <span className="flex h-[56px] w-[56px] items-center justify-center rounded-full border-[0.5px] border-[#5D5D5D] font-medium text-[#3D3D3D]">
                  <FaApple className="h-6 w-6" />
                </span>
              </button>
              <button>
                <span className="flex h-[56px] w-[56px] items-center justify-center rounded-full bg-[#060742] font-medium text-white">
                  <FaGithub className="h-6 w-6" />
                </span>
              </button>
            </div>
            <span className="mt-3 block text-sm text-[#3D3D3D]">
              Bằng việc tiếp tục, bạn đồng ý với{" "}
              <span className="text-[#DD3F24]">Điều khoản sử dụng</span> và{" "}
              <span className="text-[#DD3F24]">Chính sách bảo mật</span> của
              TopDev.
            </span>
            <span className="mt-7 block text-lg text-[#5D5D5D]">
              Bạn là <span className="text-[#DD3F24]">Nhà Tuyển Dụng?</span> Vui
              lòng đăng nhập qua link này!
            </span>
          </div>
          <button
            onClick={() => signOut()}
            className="rounded bg-red-600 px-4 py-2 text-white"
          >
            Logout
          </button>
        </Modal.Body>
      </div>
    </Modal>
  );
};

export default LoginModal;
