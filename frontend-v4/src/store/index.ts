import { configureStore } from "@reduxjs/toolkit";
import { useSelector } from "react-redux";
import { TypedUseSelectorHook, useDispatch } from "react-redux";
import { NODE_ENV } from "@/utils/enums";
import userSlice from "./slices/userSlice";
import counterSlice from "./slices/couterSlice";
import jobSlice from "./slices/jobSlice";
import taxonomySlide from "@/store/slices/taxonomySlice";
import settingSlice from "@/store/slices/settingSlice";
import cartSlice from "@/store/slices/cartSlide";
import searchSlice from "./slices/searchSlice";
import jobCategorySlice from "./slices/jobCategorySlice";
import authSlice from "./slices/authSlide";

export const store = configureStore({
  reducer: {
    user: userSlice.reducer,
    job: jobSlice.reducer,
    counter: counterSlice.reducer,
    taxonomies: taxonomySlide.reducer,
    setting: settingSlice.reducer,
    cart: cartSlice.reducer,
    search: searchSlice.reducer,
    jobCategories: jobCategorySlice.reducer,
    auth: authSlice.reducer,
  },
  devTools: NODE_ENV !== "production",
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

export const useAppDispatch: () => AppDispatch = useDispatch;
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

export default store;
