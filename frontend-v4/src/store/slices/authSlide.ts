import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface CounterState {
  open: boolean;
}

const initialState: CounterState = {
  open: false,
};

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    open: (state) => {
      state.open = true;
    },
    closePopup: (state) => {
      state.open = false;
    },
  },
});

export const { open, closePopup } = authSlice.actions;

export default authSlice;
