"use client";

import { useSession } from "next-auth/react";
import { useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import axios from "axios";

// Extend the NextAuth session type to include accessToken
declare module "next-auth" {
  interface Session {
    user?: {
      name?: string | null;
      email?: string | null;
      image?: string | null;
      accessToken?: string | null;
    };
  }
}

export default function AuthCallback() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const searchParams = useSearchParams();

  const redirect = searchParams.get("redirect") || "/"; // default path

  useEffect(() => {
    const sendToBackend = async () => {
      if (status === "authenticated" && session?.user) {
        try {
          console.log(session, "session");

          router.replace(redirect);
        } catch (err) {
          console.error("Backend sync failed", err);
          router.replace("/error");
        }
      }
    };

    sendToBackend();
  }, [status, session, redirect, router]);

  return <div className="p-6 text-center">Logging in...</div>;
}
