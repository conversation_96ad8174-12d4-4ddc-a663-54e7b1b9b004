"use client";
import React from "react";
import { use<PERSON><PERSON>, Submit<PERSON><PERSON><PERSON>, UseFormRegisterReturn } from "react-hook-form";
import Image from "next/image";
import { FaUser } from "react-icons/fa";
import { IoIosLock } from "react-icons/io";
import Link from "next/link";

type LoginFormInputs = {
  email: string;
  password: string;
};
type InputWithIconProps = {
  type?: string;
  placeholder?: string;
  icon: React.ReactNode;
  error?: string;
  register: UseFormRegisterReturn;
  labelContext: string;
};
const InputWithIcon: React.FC<InputWithIconProps> = ({
  type = "text",
  placeholder,
  icon,
  error,
  register,
  labelContext = "",
}) => {
  return (
    <div className="mb-3">
      <label className="mb-1 block text-sm font-medium text-[#5D5D5D]">
        {labelContext}
      </label>
      <div className="flex items-center gap-2 rounded border border-[#C2C2C2] bg-white px-5 py-2">
        <div className="border-r border-r-[#5D5D5D] pr-2 text-[#5D5D5D]">
          {icon}
        </div>
        <input
          type={type}
          placeholder={placeholder}
          className="w-[532px] flex-1 border-none bg-transparent text-sm outline-none"
          {...register}
        />
      </div>
      {error && <p className="mt-1 text-xs text-red-500">{error}</p>}
    </div>
  );
};
const LoginForm: React.FC = () => {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<LoginFormInputs>();

  const onSubmit: SubmitHandler<LoginFormInputs> = async (data) => {
    console.log("Login data:", data);
    await new Promise((resolve) => setTimeout(resolve, 1000));
  };

  return (
    <div className="flex w-1/2 items-center justify-center rounded-lg p-6">
      <div className="flex w-[736px] flex-col items-center rounded-[16px] border border-[#DD3F24] px-24 py-10">
        <Image
          src="https://c.topdevvn.com/uploads/2025/07/23/login.png"
          width={181}
          height={164}
          alt="login"
        />
        <span className="mt-6 block text-[32px] font-bold text-[#DD3F24]">
          Welcome back, Employer!
        </span>
        <span className="mb-6 mt-4 block text-lg text-[#5D5D5D]">
          Access your dashboard, review applicants, and post new jobs.
        </span>
        <form className="mb-6" onSubmit={handleSubmit(onSubmit)} noValidate>
          <InputWithIcon
            icon={<FaUser />}
            labelContext="Tài khoản đăng nhập"
            placeholder="Nhập email"
            register={register("email", {
              required: "Email là bắt buộc",
              pattern: {
                value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                message: "Email không hợp lệ",
              },
            })}
            error={errors.email?.message}
          />
          <InputWithIcon
            type="password"
            labelContext="Mật khẩu"
            icon={<IoIosLock />}
            placeholder="Nhập mật khẩu"
            register={register("password", {
              required: "Mật khẩu là bắt buộc",
              minLength: {
                value: 6,
                message: "Mật khẩu ít nhất 6 ký tự",
              },
            })}
            error={errors.password?.message}
          />
          {/* Submit */}
          <Link
            href="/"
            className="block w-full text-end text-sm text-[#DD3F24]"
          >
            Quên mật khẩu?
          </Link>
          <span className="mb-4 mt-6 block text-center text-xs text-[#3D3D3D]">
            Bằng việc tiếp tục, bạn đồng ý với{" "}
            <span className="font-semibold">Điều khoản sử dụng</span> và{" "}
            <span className="font-semibold">Chính sách bảo mật</span> của
            TopDev.
          </span>
          <button
            type="submit"
            disabled={isSubmitting}
            className="w-full rounded-[64px] bg-[#DD3F24] py-4 font-semibold text-white disabled:bg-[#F6F6F6] disabled:text-[#B0B0B0]"
          >
            Đăng nhập
          </button>
          <span className="mt-[10px] block text-center text-sm">
            Bạn chưa có tài khoản? 
            <Link
              href="/emoployer/sign-in"
              className="font-semibold text-[#DD3F24]"
            >
              Đăng ký ngay
            </Link>
          </span>
        </form>
      </div>
    </div>
  );
};

export default LoginForm;
