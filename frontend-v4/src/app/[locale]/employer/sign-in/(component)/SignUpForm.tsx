"use client";
import React from "react";
import { use<PERSON><PERSON>, Submit<PERSON><PERSON><PERSON>, UseFormRegisterReturn } from "react-hook-form";
import Image from "next/image";
import Link from "next/link";

type LoginFormInputs = {
  email: string;
  phone: string;
  password: string;
  confirmPassword: string;
  taxCode: string;
  companyName: string;
  location: string;
  specialized: string;
  technical: string;
  companyNameTopdev: string;
  isHiring: string;
  agree: string;
};

type InputWithIconProps = {
  type?: string;
  placeholder?: string;
  error?: string;
  register: UseFormRegisterReturn;
  labelContext: string;
};

const InputWithIcon: React.FC<InputWithIconProps> = ({
  type = "text",
  placeholder,
  error,
  register,
  labelContext = "",
}) => {
  return (
    <div>
      <label className="mb-1 block text-sm font-medium text-[#5D5D5D]">
        {labelContext}
      </label>
      <input
        type={type}
        placeholder={placeholder}
        className="w-80 flex-1 rounded border border-[#C2C2C2] bg-transparent bg-white py-4 text-sm outline-none"
        {...register}
      />
      {error && <p className="mt-1 text-xs text-red-500">{error}</p>}
    </div>
  );
};

const SignUpForm: React.FC = () => {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    watch,
  } = useForm<LoginFormInputs>();

  const onSubmit: SubmitHandler<LoginFormInputs> = async (data) => {
    console.log("SignUp data:", data);
    await new Promise((resolve) => setTimeout(resolve, 1000));
  };

  const password = watch("password");

  return (
    <div className="flex w-1/2 items-center justify-center rounded-lg p-6">
      <div className="flex w-[663px] flex-col items-center">
        <Image
          src="https://c.topdevvn.com/uploads/2025/07/24/register.png"
          width={195}
          height={164}
          alt="register"
        />
        <span className="mt-6 block text-[32px] font-bold text-[#DD3F24]">
          Create Your Employer Account
        </span>
        <span className="mb-6 mt-4 block text-lg text-[#5D5D5D]">
          It only takes a minute to sign up — but it could change your team
          forever.
        </span>

        <form
          className="mb-6 grid grid-cols-2 gap-x-5 gap-y-2"
          onSubmit={handleSubmit(onSubmit)}
          noValidate
        >
          {/* FIRST BLOCK */}
          <span className="col-span-2 block text-sm font-semibold text-[#DD3F24]">
            Thông tin đăng nhập
          </span>
          <InputWithIcon
            labelContext="Email"
            placeholder="Nhập email"
            register={register("email", {
              required: "Email là bắt buộc",
              pattern: {
                value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                message: "Email không hợp lệ",
              },
            })}
            error={errors.email?.message}
          />
          <InputWithIcon
            labelContext="Số điện thoại"
            placeholder="Nhập số điện thoại"
            register={register("phone", {
              required: "Số điện thoại là bắt buộc",
              pattern: {
                value: /^[0-9]{9,11}$/,
                message: "Số điện thoại không hợp lệ",
              },
            })}
            error={errors.phone?.message}
          />
          <InputWithIcon
            type="password"
            labelContext="Mật khẩu"
            placeholder="Nhập mật khẩu"
            register={register("password", {
              required: "Mật khẩu là bắt buộc",
              minLength: {
                value: 6,
                message: "Mật khẩu ít nhất 6 ký tự",
              },
            })}
            error={errors.password?.message}
          />
          <InputWithIcon
            type="password"
            labelContext="Xác nhận mật khẩu"
            placeholder="Nhập lại mật khẩu"
            register={register("confirmPassword", {
              required: "Vui lòng xác nhận mật khẩu",
              validate: (value) =>
                value === password || "Mật khẩu xác nhận không khớp",
            })}
            error={errors.confirmPassword?.message}
          />
          <span className="col-span-2 block text-sm font-semibold text-[#DD3F24]">
            Thông tin công ty
          </span>
          {/* SECOND BLOCK */}
          <InputWithIcon
            labelContext="Mã số thuế"
            placeholder="Nhập mã số thuế công ty"
            register={register("taxCode", {
              required: "Mã số thuế là bắt buộc",
            })}
            error={errors.taxCode?.message}
          />
          <InputWithIcon
            labelContext="Địa chỉ công ty"
            placeholder="Nhập địa chỉ"
            register={register("location", {
              required: "Địa chỉ công ty là bắt buộc",
            })}
            error={errors.location?.message}
          />
          <InputWithIcon
            labelContext="Tên công ty"
            placeholder="Nhập tên công ty"
            register={register("companyName", {
              required: "Tên công ty là bắt buộc",
            })}
            error={errors.companyName?.message}
          />
          <InputWithIcon
            labelContext="Tên công ty hiển thị trên TopDev"
            placeholder="Nhập tên công ty hiển thị trên TopDev"
            register={register("companyNameTopdev", {
              required: "Tên công ty hiển thị trên TopDev",
            })}
            error={errors.companyNameTopdev?.message}
          />
          <InputWithIcon
            labelContext="Lĩnh vực hoạt động"
            placeholder="Ví dụ: Công nghệ thông tin"
            register={register("specialized", {
              required: "Lĩnh vực là bắt buộc",
            })}
            error={errors.specialized?.message}
          />
          <InputWithIcon
            labelContext="Công nghệ sử dụng"
            placeholder="Ví dụ: React, Node.js"
            register={register("technical", {
              required: "Vui lòng nhập công nghệ sử dụng",
            })}
            error={errors.technical?.message}
          />

          {/* Policies & Submit */}
          <div className="col-span-2 mb-3 mt-5 flex items-center gap-6">
            <label className="block text-sm font-medium text-[#5D5D5D]">
              Công ty bạn đang có nhu cầu tuyển dụng không?
            </label>
            <div className="flex gap-6">
              <label className="flex items-center gap-2 text-sm text-[#3D3D3D]">
                <input
                  type="radio"
                  value="yes"
                  {...register("isHiring", {
                    required: "Vui lòng chọn một lựa chọn",
                  })}
                  className="checked:bg-[#DD3F24] focus-within:ring-0 checked:hover:bg-[#DD3F24] checked:focus:bg-[#DD3F24]"
                />
                Có
              </label>
              <label className="flex items-center gap-2 text-sm text-[#3D3D3D]">
                <input
                  type="radio"
                  value="no"
                  {...register("isHiring", {
                    required: "Vui lòng chọn một lựa chọn",
                  })}
                  className="checked:bg-[#DD3F24] focus-within:ring-0 checked:hover:bg-[#DD3F24] checked:focus:bg-[#DD3F24]"
                />
                Không
              </label>
            </div>
            {errors.isHiring && (
              <p className="mt-1 text-xs text-red-500">
                {errors.isHiring.message}
              </p>
            )}
          </div>
          <div className="col-span-2 mb-4">
            <label className="flex items-center gap-2 text-sm text-[#3D3D3D]">
              <input
                type="checkbox"
                {...register("agree", {
                  required: "Bạn phải đồng ý với Điều khoản và Chính sách",
                })}
                className="h-4 w-4 rounded border border-[#B0B0B0] checked:bg-[#DD3F24] focus-within:ring-0 checked:hover:bg-[#DD3F24] checked:focus:bg-[#DD3F24]"
              />
              <span>
                Tôi đã đọc và đồng ý với{" "}
                <span className="font-semibold underline">
                  Điều khoản sử dụng
                </span>{" "}
                và{" "}
                <span className="font-semibold underline">
                  Chính sách bảo mật
                </span>{" "}
                của TopDev.
              </span>
            </label>
            {errors.agree && (
              <p className="mt-1 text-xs text-red-500">
                {errors.agree.message}
              </p>
            )}
          </div>
          <div className="col-span-2">
            <button
              type="submit"
              disabled={isSubmitting}
              className="w-full rounded-[64px] bg-[#DD3F24] py-4 font-semibold text-white disabled:bg-[#F6F6F6] disabled:text-[#B0B0B0]"
            >
              Đăng ký
            </button>
            <span className="mt-[10px] block text-center text-sm">
              Bạn đã có tài khoản?{" "}
              <Link href="/" className="font-semibold text-[#DD3F24]">
                Đăng nhập
              </Link>
            </span>
          </div>
        </form>
      </div>
    </div>
  );
};

export default SignUpForm;
