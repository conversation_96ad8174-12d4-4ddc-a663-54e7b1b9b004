#!/bin/bash

# Test script for Social Login Routes
# Usage: ./test_social_routes.sh [base_url]

BASE_URL=${1:-"https://accounts.topdev.vn"}

echo "🔐 Testing Social Login Routes"
echo "=============================="
echo "Base URL: $BASE_URL"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to test redirect endpoints
test_redirect() {
    local provider=$1
    echo -e "${BLUE}Testing $provider redirect...${NC}"
    
    response=$(curl -s -I "$BASE_URL/login/$provider" | head -n 1)
    
    if [[ $response == *"302"* ]] || [[ $response == *"301"* ]]; then
        echo -e "${GREEN}✅ $provider redirect working${NC}"
    else
        echo -e "${RED}❌ $provider redirect failed${NC}"
        echo "Response: $response"
    fi
    echo ""
}

# Function to test callback endpoints  
test_callback() {
    local provider=$1
    echo -e "${BLUE}Testing $provider callback...${NC}"
    
    # Test GET callback (will likely return error without code, but endpoint should exist)
    response=$(curl -s -w "%{http_code}" -o /dev/null "$BASE_URL/login/$provider/callback")
    
    if [[ $response == "200" ]] || [[ $response == "302" ]] || [[ $response == "422" ]] || [[ $response == "400" ]]; then
        echo -e "${GREEN}✅ $provider GET callback endpoint exists${NC}"
    else
        echo -e "${RED}❌ $provider GET callback failed (HTTP: $response)${NC}"
    fi
    
    # Test POST callback
    response=$(curl -s -w "%{http_code}" -o /dev/null -X POST "$BASE_URL/login/$provider/callback" \
        -H "Content-Type: application/json" \
        -d '{"test": "data"}')
    
    if [[ $response == "200" ]] || [[ $response == "302" ]] || [[ $response == "422" ]] || [[ $response == "400" ]] || [[ $response == "500" ]]; then
        echo -e "${GREEN}✅ $provider POST callback endpoint exists${NC}"
    else
        echo -e "${RED}❌ $provider POST callback failed (HTTP: $response)${NC}"
    fi
    echo ""
}

# Test one-tap endpoints
test_one_tap() {
    local provider=$1
    echo -e "${BLUE}Testing $provider one-tap...${NC}"
    
    response=$(curl -s -w "%{http_code}" -o /dev/null -X POST "$BASE_URL/$provider/one-tap" \
        -H "Content-Type: application/json" \
        -d '{"credential": "test_token"}')
    
    if [[ $response == "200" ]] || [[ $response == "302" ]] || [[ $response == "422" ]] || [[ $response == "400" ]] || [[ $response == "500" ]]; then
        echo -e "${GREEN}✅ $provider one-tap endpoint exists${NC}"
    else
        echo -e "${RED}❌ $provider one-tap failed (HTTP: $response)${NC}"
    fi
    echo ""
}

# List of providers to test
providers=("facebook" "google" "linkedin" "twitter" "github" "apple")

echo -e "${YELLOW}1. Testing Redirect Endpoints${NC}"
echo "================================"
for provider in "${providers[@]}"; do
    test_redirect $provider
done

echo -e "${YELLOW}2. Testing Callback Endpoints${NC}"
echo "==============================="
for provider in "${providers[@]}"; do
    test_callback $provider
done

echo -e "${YELLOW}3. Testing One-tap Endpoints${NC}"
echo "============================="
# One-tap usually only for Google and Facebook
for provider in "google" "facebook"; do
    test_one_tap $provider
done

echo -e "${YELLOW}4. Testing Invalid Provider${NC}"
echo "============================"
echo -e "${BLUE}Testing invalid provider...${NC}"
response=$(curl -s -w "%{http_code}" -o /dev/null "$BASE_URL/login/invalid_provider")
if [[ $response == "404" ]] || [[ $response == "500" ]]; then
    echo -e "${GREEN}✅ Invalid provider properly handled${NC}"
else
    echo -e "${RED}❌ Invalid provider not handled (HTTP: $response)${NC}"
fi
echo ""

echo -e "${GREEN}🎉 Social Login Routes Test Completed!${NC}"
echo ""
echo "Notes:"
echo "- 302/301: Redirect working (expected for OAuth flow)"
echo "- 422: Validation error (expected without real tokens)"
echo "- 400: Bad request (expected with test data)"
echo "- 500: Server error (may indicate missing config)"
echo "- 404: Route not found (indicates routing issue)"
echo ""
echo "For detailed testing with real tokens, use browser or Postman"
