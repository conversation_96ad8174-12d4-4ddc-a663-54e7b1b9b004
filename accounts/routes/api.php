<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::post('changepassword', 'Auth\ResetPasswordController@changePassword')->middleware('auth:api');

Route::get('v1/user/request-delete', 'Auth\DeleteController@magicLink')->middleware('auth:api');

/**
 * Tracking activity for signup event which blocks by adblock
 */
Route::post('v1/activity', [App\Http\Controllers\Api\ActivityController::class, 'tracking'])
    ->name('api.v1.activity');

// ==================== AUTHENTICATION API ROUTES ====================

Route::prefix('auth')->group(function () {
    // Basic authentication
    Route::post('/login', 'Auth\LoginController@apiLogin')
        ->name('api.auth.login');

    // Social authentication
    Route::get('/social/{provider}', 'Auth\LoginController@apiGetSocialRedirectUrl')
        ->name('api.auth.social.redirect');

    Route::post('/social/{provider}/callback', 'Auth\LoginController@apiHandleSocialCallback')
        ->name('api.auth.social.callback');
});
