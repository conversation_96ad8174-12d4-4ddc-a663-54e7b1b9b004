# Social Login Guide

## Tổng quan

LoginController đã hỗ trợ đầy đủ social login với các providers:
- ✅ **Facebook** - OAuth 2.0
- ✅ **Twitter** - OAuth 1.0a (đã có logic xử lý đặc biệt)
- ✅ **LinkedIn** - OAuth 2.0  
- ✅ **Google** - OAuth 2.0
- ✅ **GitHub** - OAuth 2.0
- ✅ **Apple** - OAuth 2.0 với JWT

## Routes hiện có

### Web Routes (đã có sẵn)
```php
// Redirect to provider
Route::get('login/{provider}', 'Auth\LoginController@redirectToProvider');

// Handle callback (GET và POST)
Route::get('login/{provider}/callback', 'Auth\LoginController@handleProviderCallback');
Route::post('login/{provider}/callback', 'Auth\LoginController@handleProviderCallback');

// One-tap login (Google, Facebook)
Route::post('/{provider}/one-tap', "Auth\LoginController@oneTap");
```

## Cách sử dụng

### 1. Web OAuth Flow (Standard)

**Step 1:** Redirect user đến provider
```
GET /login/facebook
GET /login/google  
GET /login/linkedin
GET /login/twitter
GET /login/github
GET /login/apple
```

**Step 2:** Provider redirect về callback URL với authorization code
```
GET /login/{provider}/callback?code=authorization_code
```

### 2. One-tap Login (Google, Facebook)

Dành cho Google One Tap và Facebook Login Button:
```javascript
// Frontend gửi credential token
POST /{provider}/one-tap
Content-Type: application/json

{
    "credential": "google_credential_token"
    // hoặc
    "data": {
        "credential": "facebook_access_token"
    }
}
```

### 3. Manual Token Flow

Nếu frontend đã có access token từ provider SDK:
```
POST /login/{provider}/callback
Content-Type: application/json

{
    "access_token": "provider_access_token"
}
```

**Đặc biệt với Twitter (OAuth 1.0a):**
```
POST /login/twitter/callback  
Content-Type: application/json

{
    "oauth_token": "twitter_oauth_token",
    "oauth_verifier": "twitter_oauth_verifier"
}
```

## Configuration

Đảm bảo các environment variables được cấu hình trong `.env`:

```env
# Facebook
FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret
FACEBOOK_APP_CALLBACK_URL=https://accounts.topdev.vn/login/facebook/callback

# Twitter  
TWITTER_APP_ID=your_twitter_app_id
TWITTER_APP_SECRET=your_twitter_app_secret
TWITTER_APP_CALLBACK_URL=https://accounts.topdev.vn/login/twitter/callback

# LinkedIn
LINKEDIN_APP_ID=your_linkedin_app_id
LINKEDIN_APP_SECRET=your_linkedin_app_secret
LINKEDIN_APP_CALLBACK_URL=https://accounts.topdev.vn/login/linkedin/callback

# Google
GOOGLE_APP_ID=your_google_app_id
GOOGLE_APP_SECRET=your_google_app_secret
GOOGLE_APP_CALLBACK_URL=https://accounts.topdev.vn/login/google/callback

# GitHub
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret
GITHUB_APP_CALLBACK_URL=https://accounts.topdev.vn/login/github/callback

# Apple
SIGN_IN_WITH_APPLE_CLIENT_ID=your_apple_client_id
SIGN_IN_WITH_APPLE_CLIENT_SECRET=your_apple_client_secret
SIGN_IN_WITH_APPLE_REDIRECT=https://accounts.topdev.vn/login/apple/callback
```

## Logic xử lý hiện có

### 1. `redirectToProvider()` 
- Lưu query parameters vào session
- Redirect user đến provider authorization URL
- Hỗ trợ tất cả providers

### 2. `handleProviderCallback()`
- Lấy user info từ provider
- Kiểm tra user đã tồn tại chưa
- Nếu chưa có → hiển thị form verify-user
- Nếu có rồi → đăng nhập và redirect

### 3. `oneTap()`
- Xử lý Google One Tap và Facebook Login Button
- Sử dụng `userFromToken()` để lấy user info
- Logic tương tự `handleProviderCallback()`

### 4. `verifyUser()`
- Xử lý khi user xác nhận thông tin từ social provider
- Tạo user mới hoặc link với user existing
- Complete user setup (profile, CV, search candidate)

### 5. `getResourceOwner()`
- Helper method để lấy thông tin chi tiết từ provider
- Xử lý đặc biệt cho Twitter (OAuth 1.0a)

## Provider-specific Notes

### Facebook
- Reliable email và profile info
- Hỗ trợ cả OAuth flow và token-based
- One-tap login available

### Twitter
- OAuth 1.0a (khác với các provider khác)
- Email không luôn có sẵn (tùy app permissions)
- Cần xử lý `userFromTokenAndSecret()`

### LinkedIn  
- Professional network
- Reliable email và profile info
- Good for B2B applications

### Google
- Most reliable provider
- One-tap login available  
- Always returns email

### GitHub
- Developer-focused
- Email có thể private
- Good for tech recruitment

### Apple
- Privacy-focused
- Có thể dùng relay email
- Requires special JWT handling

## Testing

### Test với browser:
```
https://accounts.topdev.vn/login/facebook
https://accounts.topdev.vn/login/google
https://accounts.topdev.vn/login/linkedin
https://accounts.topdev.vn/login/twitter
https://accounts.topdev.vn/login/github
https://accounts.topdev.vn/login/apple
```

### Test với curl:
```bash
# Test redirect
curl -I "https://accounts.topdev.vn/login/facebook"

# Test callback (sẽ fail without real code)
curl -X POST "https://accounts.topdev.vn/login/facebook/callback" \
  -H "Content-Type: application/json" \
  -d '{"access_token": "test_token"}'
```

## Troubleshooting

### Common Issues:

1. **"Provider not supported"**: Kiểm tra provider name trong URL
2. **"Invalid credentials"**: Kiểm tra app credentials trong .env
3. **"Email not provided"**: Provider không trả về email (thường với Twitter)
4. **Redirect loop**: Kiểm tra callback URL configuration

### Debug Tips:

1. Check Laravel logs: `storage/logs/laravel.log`
2. Verify provider app settings (callback URLs, permissions)
3. Test với Postman hoặc browser developer tools
4. Kiểm tra session data và cookies

## Security Features

- ✅ Account freeze protection
- ✅ Automatic user creation với proper setup
- ✅ UTM tracking integration
- ✅ Multi-domain cookie support
- ✅ Comprehensive logging
- ✅ Event firing cho analytics
- ✅ CSRF protection
- ✅ Session management

## Next Steps

1. **Frontend Integration**: Update frontend để sử dụng existing routes
2. **Mobile Integration**: Sử dụng token-based flow cho mobile apps  
3. **Testing**: Test với real provider credentials
4. **Monitoring**: Setup monitoring cho social login success/failure rates
5. **Analytics**: Track conversion rates by provider
