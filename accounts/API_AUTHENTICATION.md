# Authentication API Documentation

## Tổng quan

API này cung cấp các endpoint để xử lý đăng nhập qua email/password và các social providers như Facebook, Twitter, LinkedIn, Google, GitHub, và Apple.

## Base URL
```
https://accounts.topdev.vn/api
```

## Supported Social Providers

- `facebook` - Facebook Login
- `twitter` - Twitter/X.com Login  
- `linkedin` - LinkedIn Login
- `google` - Google Login
- `github` - GitHub Login
- `apple` - Apple Sign In

## API Endpoints

### 1. Basic Login (Email/Password)

**Endpoint:** `POST /api/auth/login`

**Request Body:**
```json
{
    "email": "<EMAIL>",
    "password": "password123"
}
```

**Success Response:**
```json
{
    "success": true,
    "message": "Login successful",
    "data": {
        "user": {
            "id": "uuid",
            "email": "<EMAIL>",
            "display_name": "<PERSON>",
            "type": "resume",
            "is_employer": false,
            "has_verified_email": true,
            "has_approved_account": true
        },
        "access_token": "api_access_token",
        "token_type": "Bearer"
    }
}
```

**Error Response:**
```json
{
    "success": false,
    "message": "Invalid credentials"
}
```

### 2. Get Social Login URL

**Endpoint:** `GET /api/auth/social/{provider}`

**Parameters:**
- `provider` (path): One of: facebook, twitter, linkedin, google, github, apple

**Response:**
```json
{
    "success": true,
    "message": "Redirect URL generated successfully", 
    "data": {
        "redirect_url": "https://provider.com/oauth/authorize?..."
    }
}
```

**Error Response:**
```json
{
    "success": false,
    "message": "Unsupported provider"
}
```

### 3. Handle Social Login Callback

**Endpoint:** `POST /api/auth/social/{provider}/callback`

**Parameters:**
- `provider` (path): One of: facebook, twitter, linkedin, google, github, apple

#### Request Body Options:

**OAuth Code Flow:**
```json
{
    "code": "authorization_code_from_provider"
}
```

**Token Flow (Facebook, Google, LinkedIn, GitHub, Apple):**
```json
{
    "access_token": "access_token_from_provider"
}
```

**Token Flow (Twitter):**
```json
{
    "access_token": "oauth_token",
    "access_token_secret": "oauth_token_secret"
}
```

**Success Response:**
```json
{
    "success": true,
    "message": "Social login successful",
    "data": {
        "user": {
            "id": "uuid",
            "email": "<EMAIL>",
            "display_name": "John Doe",
            "type": "resume",
            "is_employer": false,
            "has_verified_email": true,
            "has_approved_account": true
        },
        "access_token": "api_access_token",
        "token_type": "Bearer",
        "provider": "facebook"
    }
}
```

**Error Responses:**
```json
{
    "success": false,
    "message": "Unsupported provider"
}
```

```json
{
    "success": false,
    "message": "Email is required from social provider"
}
```

```json
{
    "success": false,
    "message": "Account is frozen"
}
```

## Authentication Flow Examples

### Web Application Flow

1. **Get Authorization URL:**
```bash
curl -X GET "https://accounts.topdev.vn/api/auth/social/facebook"
```

2. **Redirect user to the returned URL**

3. **Handle callback with authorization code:**
```bash
curl -X POST "https://accounts.topdev.vn/api/auth/social/facebook/callback" \
  -H "Content-Type: application/json" \
  -d '{"code": "authorization_code_from_facebook"}'
```

### Mobile Application Flow

1. **Get access token from provider's SDK**

2. **Send token to API:**
```bash
curl -X POST "https://accounts.topdev.vn/api/auth/social/facebook/callback" \
  -H "Content-Type: application/json" \
  -d '{"access_token": "facebook_access_token"}'
```

### Twitter Flow (OAuth 1.0a)

```bash
curl -X POST "https://accounts.topdev.vn/api/auth/social/twitter/callback" \
  -H "Content-Type: application/json" \
  -d '{
    "access_token": "oauth_token",
    "access_token_secret": "oauth_token_secret"
  }'
```

## Provider-specific Notes

### Facebook
- Requires Facebook App ID and Secret
- Supports both OAuth code flow and token-based authentication
- Returns user's name, email, and profile picture
- Reliable email access

### Twitter/X.com
- Uses OAuth 1.0a (requires token and token_secret)
- May not always return email (depends on app permissions)
- Requires special handling for token-based auth

### LinkedIn
- Professional network integration
- Supports both OAuth code flow and token-based authentication
- Returns professional profile information
- Reliable email and name

### Google
- Most reliable provider
- Supports both OAuth code flow and token-based authentication
- Always returns email and basic profile info
- High user trust

### GitHub
- Developer-focused platform
- May not return email if user's email is private
- Returns username and public profile info

### Apple
- Privacy-focused provider
- Uses JWT-based authentication
- May provide relay email for privacy
- Requires special configuration

## Error Handling

All endpoints return consistent error responses:

```json
{
    "success": false,
    "message": "Error description"
}
```

**Common HTTP Status Codes:**
- `200` - Success
- `400` - Bad Request (invalid provider, missing parameters)
- `401` - Unauthorized (invalid credentials)
- `403` - Forbidden (account frozen)
- `500` - Internal Server Error

## Security Features

1. **Account Freeze Protection**: Frozen accounts cannot login
2. **Email Validation**: Social providers must provide email
3. **Automatic User Creation**: New users are created automatically
4. **Multi-domain Cookies**: Supports multiple subdomains
5. **Login Logging**: All login attempts are logged
6. **Event Tracking**: Login events are fired for analytics

## Configuration

Ensure these environment variables are set:

```env
# Facebook
FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret

# Twitter
TWITTER_APP_ID=your_twitter_app_id
TWITTER_APP_SECRET=your_twitter_app_secret

# LinkedIn
LINKEDIN_APP_ID=your_linkedin_app_id
LINKEDIN_APP_SECRET=your_linkedin_app_secret

# Google
GOOGLE_APP_ID=your_google_app_id
GOOGLE_APP_SECRET=your_google_app_secret

# GitHub
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret

# Apple
SIGN_IN_WITH_APPLE_CLIENT_ID=your_apple_client_id
SIGN_IN_WITH_APPLE_CLIENT_SECRET=your_apple_client_secret
```

## Testing

Use tools like Postman or curl to test the endpoints:

```bash
# Test basic login
curl -X POST "https://accounts.topdev.vn/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}'

# Test social login URL
curl -X GET "https://accounts.topdev.vn/api/auth/social/facebook"

# Test social callback
curl -X POST "https://accounts.topdev.vn/api/auth/social/facebook/callback" \
  -H "Content-Type: application/json" \
  -d '{"access_token": "facebook_token"}'
```
