<?php

/**
 * Test script for Authentication API
 * 
 * Usage: php test_auth_api.php
 */

class AuthApiTester
{
    private $baseUrl;
    
    public function __construct($baseUrl = 'http://localhost:8000/api')
    {
        $this->baseUrl = rtrim($baseUrl, '/');
    }
    
    /**
     * Make HTTP request
     */
    private function makeRequest($method, $endpoint, $data = null, $headers = [])
    {
        $url = $this->baseUrl . $endpoint;
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        // Set headers
        $defaultHeaders = ['Content-Type: application/json'];
        $allHeaders = array_merge($defaultHeaders, $headers);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $allHeaders);
        
        // Set method and data
        switch (strtoupper($method)) {
            case 'POST':
                curl_setopt($ch, CURLOPT_POST, true);
                if ($data) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                }
                break;
            case 'GET':
                // GET is default
                break;
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            return [
                'success' => false,
                'error' => $error,
                'http_code' => $httpCode
            ];
        }
        
        return [
            'success' => true,
            'data' => json_decode($response, true),
            'http_code' => $httpCode,
            'raw_response' => $response
        ];
    }
    
    /**
     * Test basic login
     */
    public function testBasicLogin($email = '<EMAIL>', $password = 'password')
    {
        echo "🔐 Testing Basic Login...\n";
        
        $response = $this->makeRequest('POST', '/auth/login', [
            'email' => $email,
            'password' => $password
        ]);
        
        $this->printResult('Basic Login', $response);
        return $response;
    }
    
    /**
     * Test social login URL generation
     */
    public function testSocialLoginUrl($provider = 'facebook')
    {
        echo "🔗 Testing Social Login URL Generation for {$provider}...\n";
        
        $response = $this->makeRequest('GET', "/auth/social/{$provider}");
        
        $this->printResult("Social Login URL ({$provider})", $response);
        return $response;
    }
    
    /**
     * Test social login callback (mock)
     */
    public function testSocialCallback($provider = 'facebook', $mockToken = 'mock_token')
    {
        echo "📱 Testing Social Callback for {$provider}...\n";
        
        $data = ['access_token' => $mockToken];
        
        // For Twitter, use different format
        if ($provider === 'twitter') {
            $data = [
                'access_token' => $mockToken,
                'access_token_secret' => 'mock_secret'
            ];
        }
        
        $response = $this->makeRequest('POST', "/auth/social/{$provider}/callback", $data);
        
        $this->printResult("Social Callback ({$provider})", $response);
        return $response;
    }
    
    /**
     * Test all supported providers
     */
    public function testAllProviders()
    {
        $providers = ['facebook', 'twitter', 'google', 'linkedin', 'github', 'apple'];
        
        echo "🌐 Testing all social providers...\n\n";
        
        foreach ($providers as $provider) {
            echo "--- Testing {$provider} ---\n";
            $this->testSocialLoginUrl($provider);
            $this->testSocialCallback($provider);
            echo "\n";
        }
    }
    
    /**
     * Test invalid provider
     */
    public function testInvalidProvider()
    {
        echo "❌ Testing Invalid Provider...\n";
        
        $response = $this->makeRequest('GET', '/auth/social/invalid_provider');
        
        $this->printResult('Invalid Provider', $response);
        return $response;
    }
    
    /**
     * Print test result
     */
    private function printResult($testName, $response)
    {
        echo "Test: {$testName}\n";
        echo "HTTP Code: {$response['http_code']}\n";
        
        if ($response['success']) {
            echo "Response: " . json_encode($response['data'], JSON_PRETTY_PRINT) . "\n";
            
            // Check if API response indicates success
            if (isset($response['data']['success'])) {
                if ($response['data']['success']) {
                    echo "✅ API Success\n";
                } else {
                    echo "❌ API Error: " . ($response['data']['message'] ?? 'Unknown error') . "\n";
                }
            }
        } else {
            echo "❌ Request Error: {$response['error']}\n";
        }
        
        echo "---\n\n";
    }
    
    /**
     * Run all tests
     */
    public function runAllTests()
    {
        echo "🚀 Starting Authentication API Tests\n";
        echo "Base URL: {$this->baseUrl}\n\n";
        
        // Test basic login
        $this->testBasicLogin();
        
        // Test social login URLs
        $this->testSocialLoginUrl('facebook');
        $this->testSocialLoginUrl('google');
        $this->testSocialLoginUrl('twitter');
        
        // Test social callbacks (will fail without real tokens, but tests the endpoint)
        $this->testSocialCallback('facebook');
        $this->testSocialCallback('google');
        $this->testSocialCallback('twitter');
        
        // Test invalid provider
        $this->testInvalidProvider();
        
        echo "✅ All tests completed!\n";
    }
}

// Run tests if script is executed directly
if (php_sapi_name() === 'cli') {
    $baseUrl = $argv[1] ?? 'http://localhost:8000/api';
    
    echo "Authentication API Tester\n";
    echo "========================\n\n";
    
    $tester = new AuthApiTester($baseUrl);
    $tester->runAllTests();
}
