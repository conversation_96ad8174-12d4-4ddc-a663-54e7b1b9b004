#!/bin/bash

# Quick test script for Authentication API
# Usage: ./quick_test.sh [base_url]

BASE_URL=${1:-"http://localhost:8000/api"}

echo "🚀 Quick Authentication API Test"
echo "================================="
echo "Base URL: $BASE_URL"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to make HTTP request and show result
test_endpoint() {
    local method=$1
    local endpoint=$2
    local data=$3
    local description=$4
    
    echo -e "${BLUE}Testing: $description${NC}"
    echo "Endpoint: $method $endpoint"
    
    if [ -n "$data" ]; then
        echo "Data: $data"
        response=$(curl -s -w "\n%{http_code}" -X $method "$BASE_URL$endpoint" \
            -H "Content-Type: application/json" \
            -d "$data")
    else
        response=$(curl -s -w "\n%{http_code}" -X $method "$BASE_URL$endpoint")
    fi
    
    # Split response and HTTP code
    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)
    
    echo "HTTP Code: $http_code"
    
    if [ $http_code -eq 200 ]; then
        echo -e "${GREEN}✅ Success${NC}"
        echo "Response: $body" | jq . 2>/dev/null || echo "Response: $body"
    else
        echo -e "${RED}❌ Failed${NC}"
        echo "Response: $body"
    fi
    
    echo ""
    echo "---"
    echo ""
}

# Test 1: Basic Login (will likely fail without real credentials)
echo -e "${YELLOW}1. Testing Basic Login${NC}"
test_endpoint "POST" "/auth/login" '{"email":"<EMAIL>","password":"password"}' "Basic Login"

# Test 2: Social Login URLs
echo -e "${YELLOW}2. Testing Social Login URLs${NC}"

providers=("facebook" "google" "twitter" "linkedin" "github" "apple")

for provider in "${providers[@]}"; do
    test_endpoint "GET" "/auth/social/$provider" "" "Social Login URL - $provider"
done

# Test 3: Invalid Provider
echo -e "${YELLOW}3. Testing Invalid Provider${NC}"
test_endpoint "GET" "/auth/social/invalid_provider" "" "Invalid Provider"

# Test 4: Social Callbacks (will fail without real tokens)
echo -e "${YELLOW}4. Testing Social Callbacks (Mock)${NC}"

# Facebook callback
test_endpoint "POST" "/auth/social/facebook/callback" '{"access_token":"mock_token"}' "Facebook Callback"

# Twitter callback (different format)
test_endpoint "POST" "/auth/social/twitter/callback" '{"access_token":"mock_token","access_token_secret":"mock_secret"}' "Twitter Callback"

# Google callback
test_endpoint "POST" "/auth/social/google/callback" '{"access_token":"mock_token"}' "Google Callback"

echo -e "${GREEN}🎉 Quick test completed!${NC}"
echo ""
echo "Note: Some tests may fail if:"
echo "- Server is not running"
echo "- Database is not configured"
echo "- Social provider credentials are not set"
echo "- Real tokens are required for social callbacks"
echo ""
echo "For detailed testing, run: php test_auth_api.php"
