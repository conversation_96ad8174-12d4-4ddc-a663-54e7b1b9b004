<?php

namespace Modules\User\Entities;

use App\Entities\Client;
use App\Entities\Token;
use App\Helpers\CrmApi;
use App\Notifications\NotificationEmployerLogin;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Carbon;
use Laravel\Passport\HasApiTokens;
use Modules\AuthenLog\Entities\AuthenLog;
use Modules\AuthenLog\Traits\AuthenticationLogable;
use Modules\User\Events\SignUp;
use Modules\User\Events\UserHasRecentlyUpdateProfile;
use Modules\User\Notifications\SendResetPassword;
use Modules\User\Notifications\SendUpdatePassword;
use Modules\User\Notifications\VerifyEmail;
use Modules\User\Traits\ChangeEmailable;
use Modules\User\Traits\MustApproveAccount;
use Plank\Metable\Metable;
use Str;

/**
 * Modules\User\Entities\User
 *
 * @property int $id
 * @property string $uuid
 * @property string|null $firstname
 * @property string|null $lastname
 * @property string|null $display_name
 * @property string $username
 * @property string $email
 * @property string|null $phone
 * @property string|null $gender male|female|homosexual
 * @property string|null $birthday
 * @property string|null $password
 * @property string|null $description
 * @property string|null $type using resume|employer|partner|others
 * @property string|null $remember_token
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property Carbon|null $deleted_at
 * @property int|null $company_id
 * @property Carbon|null $approved_at
 * @property Carbon|null $email_verified_at
 * @property string|null $position
 * @property string|null $freeze_at
 * @property int $allow_share_cv
 * @property-read Collection|Token[] $aliveTokens
 * @property-read UserProfile|null $userProfile
 * @property-read int|null $alive_tokens_count
 * @property-read Collection|AuthenLog[] $authentications
 * @property-read int|null $authentications_count
 * @property-read Collection|Client[] $clients
 * @property-read int|null $clients_count
 * @property-read \Modules\User\Entities\Company|null $company
 * @property-read string $full_name
 * @property-read string $short_name
 * @property-read Collection|Meta[] $meta
 * @property-read int|null $meta_count
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection|\Illuminate\Notifications\DatabaseNotification[] $notifications
 * @property-read int|null $notifications_count
 * @property-read Collection|\Modules\User\Entities\Social[] $socials
 * @property-read int|null $socials_count
 * @property-read Collection|Token[] $tokens
 * @property-read int|null $tokens_count
 * @method static Builder|User approved()
 * @method static Builder|User newModelQuery()
 * @method static Builder|User newQuery()
 * @method static \Illuminate\Database\Query\Builder|User onlyTrashed()
 * @method static Builder|User orderByMeta(string $key, string $direction = 'asc', bool $strict = false)
 * @method static Builder|User orderByMetaNumeric(string $key, string $direction = 'asc', bool $strict = false)
 * @method static Builder|User query()
 * @method static Builder|User whereAllowShareCv($value)
 * @method static Builder|User whereApprovedAt($value)
 * @method static Builder|User whereBirthday($value)
 * @method static Builder|User whereCompanyId($value)
 * @method static Builder|User whereCreatedAt($value)
 * @method static Builder|User whereDeletedAt($value)
 * @method static Builder|User whereDescription($value)
 * @method static Builder|User whereDisplayName($value)
 * @method static Builder|User whereDoesntHaveMeta($key)
 * @method static Builder|User whereEmail($value)
 * @method static Builder|User whereEmailVerifiedAt($value)
 * @method static Builder|User whereFirstname($value)
 * @method static Builder|User whereFreezeAt($value)
 * @method static Builder|User whereGender($value)
 * @method static Builder|User whereHasMeta($key)
 * @method static Builder|User whereHasMetaKeys(array $keys)
 * @method static Builder|User whereId($value)
 * @method static Builder|User whereLastname($value)
 * @method static Builder|User whereMeta(string $key, $operator, $value = null)
 * @method static Builder|User whereMetaIn(string $key, array $values)
 * @method static Builder|User whereMetaNumeric(string $key, string $operator, $value)
 * @method static Builder|User wherePassword($value)
 * @method static Builder|User wherePhone($value)
 * @method static Builder|User wherePosition($value)
 * @method static Builder|User whereRememberToken($value)
 * @method static Builder|User whereType($value)
 * @method static Builder|User whereUpdatedAt($value)
 * @method static Builder|User whereUsername($value)
 * @method static Builder|User whereUuid($value)
 * @method static \Illuminate\Database\Query\Builder|User withTrashed()
 * @method static \Illuminate\Database\Query\Builder|User withoutTrashed()
 * @mixin \Eloquent
 * @property string|null $utm_source
 * @property string|null $utm_medium
 * @property string|null $utm_campaign
 * @property string|null $utm_content
 * @property string|null $utm_term
 * @property string|null $event_group
 * @property string|null $event_name
 * @property string|null $tracking_page
 * @property string|null $tracking_location
 * @method static Builder|User whereEventGroup($value)
 * @method static Builder|User whereEventName($value)
 * @method static Builder|User whereTrackingLocation($value)
 * @method static Builder|User whereTrackingPage($value)
 * @method static Builder|User whereUtmCampaign($value)
 * @method static Builder|User whereUtmContent($value)
 * @method static Builder|User whereUtmMedium($value)
 * @method static Builder|User whereUtmSource($value)
 * @method static Builder|User whereUtmTerm($value)
 */
class User extends Authenticatable
{
    use Metable;
    use Notifiable;
    use HasApiTokens;
    use AuthenticationLogable;
    use SoftDeletes;
    use MustApproveAccount;
    use ChangeEmailable;

    /**
     * The name of the "approved at" column.
     *
     * @var string
     */
    public const APPROVED_AT = 'approved_at';

    //type of user
    public const RESUME_TYPE = 'resume';
    public const EMPLOYER_TYPE = 'employer';
    public const PARTNER_TYPE = 'partner';
    public const OTHER_TYPE = 'other';
    public const ADMINISTRATOR_TYPE = 'administrator';

    /**
     * The connection name for the model.
     *
     * @var string
     */
    protected $connection = 'applancer_ms';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'display_name',
        'type',
        'username',
        'email',
        'password',
        'remember_token',
        'approved_at',
        'email_verified_at',
        'utm_source',
        'utm_medium',
        'utm_campaign',
        'utm_content',
        'utm_term',
        'event_group',
        'event_name',
        'tracking_page',
        'tracking_location',
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password', 'remember_token',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'approved_at' => 'datetime',
        'email_verified_at' => 'datetime',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = ['full_name'];

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    protected static function boot()
    {
        parent::boot();
        static::creating(function ($user) {
            if (empty($user->username)) {
                $user->username = $user->generateUsername(
                    strstr($user->email, '@', true)
                );
            }

            // Make sure user have display name for searching in search cv function.
            // For github, if not set display name, it will be emptied
            if (empty($user->display_name)) {
                $user->display_name = $user->username;
            }
        });

        static::creating(function ($model) {
            $model->uuid = Str::uuid();
        });

        static::created(function ($user) {
            event(new SignUp($user));
            event(new UserHasRecentlyUpdateProfile($user));
        });

        static::restored(function ($user) {
            event(new UserHasRecentlyUpdateProfile($user));
        });
    }

    /**
     * Set username.
     *
     * @return void
     */
    public function setUsernameAttribute(string $username)
    {
        $this->attributes['username'] = $this->generateUsername($username);
    }

    /**
     * Lấy tên đầy đủ của user.
     *
     * @return string
     */
    public function getFullNameAttribute()
    {
        return empty($this->firstname) && empty($this->lastname)
            ? $this->display_name : $this->firstname . ' ' . $this->lastname;
    }

    /**
     * Lấy tên đầy đủ của user.
     *
     * @return string
     */
    public function getShortNameAttribute()
    {
        return strtoupper(
            mb_substr($this->firstname, 0, 1)
                . mb_substr($this->lastname, 0, 1)
        );
    }

    /**
     * Kiểm tra nếu password null thì cần update.
     *
     * @return bool
     */
    public function canUpdatePassword()
    {
        return false; //empty($this->password);
    }

    /**
     * Gửi thông báo đặt lại mật khẩu.
     *
     * @return void
     */
    public function resetPasswordUser()
    {
        $this->notify(new SendResetPassword());
    }

    /**
     * Gửi thông báo cập nhật lại mật khẩu.
     *
     * @param  string  $token
     * @return void
     */
    public function sendPasswordUpdateNotification($token)
    {
        $this->notify(new SendUpdatePassword($token));
    }

    /**
     * Gửi thông báo xác thực email employer.
     *
     * @return void
     */
    public function verifyEmailRegister()
    {
        $this->notify(new VerifyEmail());
    }

    public function company(): HasOne
    {
        return $this->hasOne(Company::class, 'id', 'company_id');
    }

    /**
     * Gửi thông báo employer login.
     *
     * @return void
     */
    public function sendEmployerLoginNotification()
    {
        $this->notify(new NotificationEmployerLogin());
        CrmApi::notifyEmployerLoginToCa($this->company_id, $this->email);
    }

    /**
     * @inheritdoc
     */
    public function routeNotificationForMail()
    {
        return $this->email;
    }

    /**
     * Set password by bcrypt.
     *
     * @return void
     */
    public function setPasswordAttribute($value)
    {
        $this->attributes['password'] = bcrypt($value);
    }

    /**
     * Generate unique username.
     */
    private function generateUsername($origin)
    {
        $username = Str::slug($origin);
        $rows = static::whereRaw("username REGEXP '^{$username}(-[0-9]*)?$'")->withTrashed()->get();
        $total = count($rows) + 1;

        return ($total > 1) ? "{$username}-{$total}" : $username;
    }

    /**
     * Check role.
     *
     * @return bool
     */
    public function role($type = 'resume')
    {
        return $this->type == $type;
    }

    /**
     * Get roles.
     *
     * @return array
     */
    public function roles()
    {
        return (array) $this->type;
    }

    public function socials(): HasMany
    {
        return $this->hasMany(Social::class, 'user_id');
    }

    public function userProfile(): HasOne
    {
        return $this->hasOne(UserProfile::class, 'user_id');
    }

    /**
     * @return mixed
     */
    public function aliveTokens()
    {
        return $this->tokens()->alive();
    }

    public function hasPermission($value): bool
    {
        $permission = [
            static::RESUME_TYPE,
            static::EMPLOYER_TYPE,
            static::PARTNER_TYPE,
            static::OTHER_TYPE,
            static::ADMINISTRATOR_TYPE,
        ];
        if (!empty($value) && in_array($value, $permission) && $this->type == $value) {
            return true;
        }

        return false;
    }

    public function markAccountAsFreezing(): bool
    {
        return $this->forceFill([
            'freeze_at' => $this->freshTimestamp(),
        ])->save();
    }

    /**
     * Mark account as unfreezing.
     *
     * @return bool
     */
    public function isFreezing(): bool
    {
        return !empty($this->freeze_at);
    }

    /**
     * Check if user is employer
     *
     * @return bool
     */
    public function isEmployer(): bool
    {
        return $this->type === 'employer';
    }
}
