# Authentication API Setup Guide

## Tổng quan

Dự án này đã được cập nhật để hỗ trợ API authentication bao gồm:
- ✅ Basic login với email/password
- ✅ Social login với Facebook, Twitter, LinkedIn, Google, GitHub, Apple
- ✅ Token-based authentication cho mobile apps
- ✅ OAuth code flow cho web applications

## Những gì đã thêm

### 1. API Methods trong LoginController

**File:** `app/Http/Controllers/Auth/LoginController.php`

Đã thêm các methods:
- `apiLogin()` - Đăng nhập bằng email/password
- `apiGetSocialRedirectUrl()` - Lấy URL redirect cho social login
- `apiHandleSocialCallback()` - Xử lý callback từ social providers
- `getSocialUserForApi()` - Helper method để lấy thông tin user từ social provider
- `findOrCreateUserForApi()` - Helper method để tìm hoặc tạo user mới
- `setAuthCookies()` - Helper method để set authentication cookies
- `logApiLogin()` - Helper method để log API login

### 2. API Routes

**File:** `routes/api.php`

Đã thêm routes:
```php
Route::prefix('auth')->group(function () {
    // Basic authentication
    Route::post('/login', 'Auth\LoginController@apiLogin');

    // Social authentication
    Route::get('/social/{provider}', 'Auth\LoginController@apiGetSocialRedirectUrl');
    Route::post('/social/{provider}/callback', 'Auth\LoginController@apiHandleSocialCallback');
});
```

### 3. Documentation

- `API_AUTHENTICATION.md` - Chi tiết về cách sử dụng API
- `test_auth_api.php` - Script test API endpoints

## Cách sử dụng

### 1. Basic Login API

```bash
curl -X POST "http://localhost:8000/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}'
```

### 2. Social Login - Web Flow

**Step 1:** Lấy authorization URL
```bash
curl -X GET "http://localhost:8000/api/auth/social/facebook"
```

**Step 2:** Redirect user đến URL trả về

**Step 3:** Handle callback với authorization code
```bash
curl -X POST "http://localhost:8000/api/auth/social/facebook/callback" \
  -H "Content-Type: application/json" \
  -d '{"code": "authorization_code_from_facebook"}'
```

### 3. Social Login - Mobile Flow

**Với Facebook, Google, LinkedIn, GitHub, Apple:**
```bash
curl -X POST "http://localhost:8000/api/auth/social/facebook/callback" \
  -H "Content-Type: application/json" \
  -d '{"access_token": "facebook_access_token"}'
```

**Với Twitter (OAuth 1.0a):**
```bash
curl -X POST "http://localhost:8000/api/auth/social/twitter/callback" \
  -H "Content-Type: application/json" \
  -d '{
    "access_token": "oauth_token",
    "access_token_secret": "oauth_token_secret"
  }'
```

## Supported Providers

| Provider | OAuth Version | Token Support | Email Reliability |
|----------|---------------|---------------|-------------------|
| Facebook | 2.0 | ✅ | High |
| Google | 2.0 | ✅ | High |
| LinkedIn | 2.0 | ✅ | High |
| GitHub | 2.0 | ✅ | Medium* |
| Apple | 2.0 (JWT) | ✅ | High** |
| Twitter | 1.0a | ✅ | Low*** |

*GitHub: Email có thể private  
**Apple: Có thể dùng relay email  
***Twitter: Email không luôn có sẵn

## Testing

### 1. Chạy test script
```bash
php test_auth_api.php
```

### 2. Test với custom URL
```bash
php test_auth_api.php "https://accounts.topdev.vn/api"
```

### 3. Test individual endpoints
```bash
# Test basic login
curl -X POST "http://localhost:8000/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}'

# Test social URL generation
curl -X GET "http://localhost:8000/api/auth/social/facebook"

# Test invalid provider
curl -X GET "http://localhost:8000/api/auth/social/invalid"
```

## Response Format

### Success Response
```json
{
    "success": true,
    "message": "Login successful",
    "data": {
        "user": {
            "id": "uuid",
            "email": "<EMAIL>",
            "display_name": "John Doe",
            "type": "resume",
            "is_employer": false,
            "has_verified_email": true,
            "has_approved_account": true
        },
        "access_token": "api_access_token",
        "token_type": "Bearer",
        "provider": "facebook"
    }
}
```

### Error Response
```json
{
    "success": false,
    "message": "Error description"
}
```

## Configuration Required

Đảm bảo các environment variables sau được cấu hình:

```env
# Facebook
FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret

# Twitter
TWITTER_APP_ID=your_twitter_app_id
TWITTER_APP_SECRET=your_twitter_app_secret

# LinkedIn
LINKEDIN_APP_ID=your_linkedin_app_id
LINKEDIN_APP_SECRET=your_linkedin_app_secret

# Google
GOOGLE_APP_ID=your_google_app_id
GOOGLE_APP_SECRET=your_google_app_secret

# GitHub
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret

# Apple
SIGN_IN_WITH_APPLE_CLIENT_ID=your_apple_client_id
SIGN_IN_WITH_APPLE_CLIENT_SECRET=your_apple_client_secret
```

## Security Features

1. **Account Freeze Protection**: Tài khoản bị đóng băng không thể đăng nhập
2. **Email Validation**: Social providers phải cung cấp email
3. **Automatic User Creation**: Tự động tạo user mới khi cần
4. **Multi-domain Cookies**: Hỗ trợ nhiều subdomain
5. **Login Logging**: Ghi log tất cả login attempts
6. **Event Tracking**: Fire events cho analytics

## Error Handling

- `400` - Bad Request (provider không hỗ trợ, thiếu parameters)
- `401` - Unauthorized (credentials không đúng)
- `403` - Forbidden (tài khoản bị đóng băng)
- `500` - Internal Server Error

## Next Steps

1. **Frontend Integration**: Cập nhật frontend để sử dụng API endpoints mới
2. **Mobile App Integration**: Tích hợp với mobile apps
3. **Testing**: Chạy comprehensive tests
4. **Monitoring**: Setup monitoring cho API endpoints
5. **Rate Limiting**: Thêm rate limiting nếu cần

## Troubleshooting

### Common Issues

1. **"Unsupported provider"**: Kiểm tra provider name trong URL
2. **"Email is required"**: Social provider không trả về email
3. **"Account is frozen"**: User account bị đóng băng
4. **500 errors**: Kiểm tra logs và configuration

### Debug Tips

1. Kiểm tra Laravel logs: `storage/logs/laravel.log`
2. Verify environment variables
3. Test với Postman hoặc curl
4. Kiểm tra social provider app settings

## Support

Nếu có vấn đề, kiểm tra:
1. `API_AUTHENTICATION.md` - Chi tiết API documentation
2. `test_auth_api.php` - Test script examples
3. Laravel logs cho error details
